<div class="department-mapping-container" [class.dialog-mode]="showAsDialog">
  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-container">
    <mat-spinner diameter="40"></mat-spinner>
  </div>

  <!-- Main Content -->
  <div *ngIf="!isLoading" class="mapping-content">
    <!-- Validation Errors -->
    <div *ngIf="hasValidationErrors" class="validation-errors">
      <mat-icon class="error-icon">error</mat-icon>
      <div class="error-list">
        <p *ngFor="let error of validationErrors" class="error-message">{{ error }}</p>
      </div>
    </div>

    <!-- Form -->
    <form [formGroup]="mappingForm" class="mapping-form">
      <div formArrayName="mappings" class="mappings-container">
        <!-- Empty State Message -->
        <div *ngIf="mappingsFormArray.controls.length === 0" class="empty-state-message">
          <mat-icon class="empty-icon">arrow_back</mat-icon>
          <p>Select departments from the left to configure category mappings</p>
        </div>

        <!-- Mapping Rows -->
        <div
          *ngFor="let mappingGroup of mappingsFormArray.controls; let i = index; trackBy: trackByDepartmentId"
          [formGroupName]="i"
          class="mapping-row"
        >
          <div class="department-name">
            <mat-icon class="department-icon">business</mat-icon>
            <span>{{ mappingGroup.get('departmentName')?.value || 'Unknown Department' }}</span>
          </div>

          <div class="categories-dropdown">
            <mat-form-field appearance="outline" class="categories-field">
              <mat-select
                formControlName="categories"
                multiple
                [placeholder]="getCategoriesPlaceholder(i)"
                (selectionChange)="onCategoriesChange(i, $event.value)"
                [disabled]="categories.length === 0"
              >
                <mat-option
                  *ngFor="let category of getAvailableCategories(i); trackBy: trackByCategory"
                  [value]="category"
                >
                  {{ category }}
                </mat-option>
              </mat-select>
              <mat-hint *ngIf="categories.length === 0">No categories available</mat-hint>
            </mat-form-field>
          </div>
        </div>


      </div>
    </form>

    <!-- Summary Section -->
    <div *ngIf="mappings.length > 0" class="mapping-summary">
      <h3>Current Mappings Summary</h3>
      <div class="summary-grid">
        <div *ngFor="let mapping of mappings" class="summary-item">
          <div class="summary-department">{{ mapping.departmentName }}</div>
          <div class="summary-categories">
            <span class="category-count">{{ mapping.categories.length }} categories</span>
            <div class="category-list">
              {{ mapping.categories.join(', ') }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Actions -->
  <div class="mapping-actions">
    <button 
      mat-button 
      type="button"
      (click)="onClose()"
      *ngIf="showAsDialog"
      class="cancel-button"
    >
      Cancel
    </button>
    
    <button 
      mat-raised-button 
      color="primary"
      type="button"
      (click)="saveMappings()"
      [disabled]="!canSave"
      class="save-button"
    >
      <mat-spinner *ngIf="isSaving" diameter="20" class="button-spinner"></mat-spinner>
      <mat-icon *ngIf="!isSaving">save</mat-icon>
      {{ isSaving ? 'Saving...' : 'Save Mappings' }}
    </button>
  </div>
</div>
