# Reconciliation Table Documentation
## Detailed Formula Breakdown and Data Sources

### Overview
The Reconciliation Table provides a comprehensive inventory reconciliation analysis that tracks inventory movements across store and kitchen locations, calculating actual consumption using the fundamental reconciliation formula.

---

## 🔢 **Core Reconciliation Formula**

### **Primary Formula:**
```
CONSUMPTION = OPENING STOCK + TRANSFERS IN - TRANSFERS OUT - CLOSING STOCK
```

### **Detailed Formula:**
```
CONSUMPTION = (Opening Store + Opening Kitchen) 
            + (Store Transfers + Kitchen Transfers) 
            - (Closing Store + Closing Kitchen)
```

---

## 📊 **Table Structure & Columns**

### **Main Reconciliation Table Columns:**
1. **Expense Head/Item** - Category/Subcategory name
2. **Opening Stock - Store (₹)** - Initial store inventory value
3. **Opening Stock - Kitchen (₹)** - Initial kitchen inventory value  
4. **Opening Stock - Total (₹)** - Combined opening stock
5. **Transfer In/Out (Store) (₹)** - Net store-level transfers
6. **Transfer In/Out (Kitchen) (₹)** - Net kitchen-level transfers
7. **Closing Stock - Store (₹)** - Final store inventory value
8. **Closing Stock - Kitchen (₹)** - Final kitchen inventory value
9. **Closing Stock - Total (₹)** - Combined closing stock
10. **Consumption (₹)** - Calculated consumption using reconciliation formula

---

## 🏪 **Store Transfer Calculations**

### **Store Transfer Formula:**
```
Store Net Transfer = Purchase + IBT In - IBT Out - Returns + Spoilage
```

### **Store Transfer Components:**
- **Purchase (₹)** - Inventory purchased from vendors (INWARD)
- **IBT In (₹)** - Inter-branch transfers received (INWARD)
- **IBT Out (₹)** - Inter-branch transfers sent (OUTWARD)
- **Return Qty (₹)** - Returns to vendors (OUTWARD)
- **Spoilage (₹)** - Store-level spoilage adjustments (±)

### **Store Transfer Breakdown Table:**
```
Total Inward = Purchase + IBT In
Total Outward = IBT Out + Returns
Net Transfer = Total Inward - Total Outward + Spoilage
```

### **Important Note:**
**Indent Out** is **NOT** included in store transfers to prevent double-counting, as it represents internal store→kitchen movements already captured in kitchen transfers.

---

## 🍳 **Kitchen Transfer Calculations**

### **Kitchen Transfer Formula:**
```
Kitchen Net Transfer = Transfer In - Transfer Out - Return To Store + Spoilage
```

### **Kitchen Transfer Components:**
- **WorkArea Transfer In (₹)** - Kitchen to kitchen transfers received (INWARD)
- **WorkArea Transfer Out (₹)** - Kitchen to kitchen transfers sent (OUTWARD)
- **Return To Store Out (₹)** - Kitchen to store returns (OUTWARD)
- **Spoilage/Adjustments (₹)** - Kitchen-level spoilage (±)
- **WorkArea Indent (₹)** - Store to kitchen transfers (EXCLUDED from kitchen transfer calculations)

### **Kitchen Transfer Breakdown Table:**
```
Total Inward = WorkArea Transfer In (Indent Excluded)
Total Outward = WorkArea Transfer Out + Return To Store Out
Net Transfer = Total Inward - Total Outward + Spoilage/Adjustments
```

---

## 📈 **Data Sources & Processing**

### **Primary Data Sources:**
1. **Store Variance Data** (`store_variance_df`)
   - Opening/Closing stock by location
   - Purchase amounts
   - IBT In/Out amounts
   - Return quantities
   - Spoilage amounts
   - Indent amounts

2. **Inventory Consumption Data** (`inventory_consumption_df`)
   - Kitchen opening/closing stock
   - WorkArea transfers
   - Kitchen spoilage adjustments
   - Transfer details

3. **Sales Data** (for Food Cost Analysis)
   - Department-wise sales
   - Category mapping

### **Data Processing Logic:**
```python
# Location Classification
is_kitchen = 'kitchen' in location.lower()
is_store = 'store' in location.lower() or 'main' in location.lower()
is_bar = 'bar' in location.lower()

# Stock Assignment
if is_kitchen:
    opening_stock_kitchen += opening_amount
    closing_stock_kitchen += closing_amount
else:  # Store/Bar/Default
    opening_stock_store += opening_amount
    closing_stock_store += closing_amount
```

---

## 🧮 **Calculation Examples**

### **Example 1: Basic Reconciliation**
```
Category: FOOD - VEGETABLES
Opening Stock (Store): ₹50,000
Opening Stock (Kitchen): ₹10,000
Store Transfers: ₹30,000 (Purchase: ₹35,000, Returns: ₹5,000)
Kitchen Transfers: ₹8,000 (Indent: ₹12,000, Return to Store: ₹4,000)
Closing Stock (Store): ₹45,000
Closing Stock (Kitchen): ₹8,000

CONSUMPTION = (50,000 + 10,000) + (30,000 + 8,000) - (45,000 + 8,000)
CONSUMPTION = 60,000 + 38,000 - 53,000 = ₹45,000
```

### **Example 2: Store Transfer Breakdown**
```
Purchase: ₹100,000 (from vendors)
IBT In: ₹20,000 (from other branches)
IBT Out: ₹15,000 (to other branches)
Returns: ₹5,000 (to vendors)
Spoilage: ₹2,000 (adjustments)

Store Net Transfer = 100,000 + 20,000 - 15,000 - 5,000 + 2,000 = ₹102,000
```

---

## ⚠️ **Critical Design Decisions**

### **Double-Counting Prevention:**
- **Indent Out** excluded from store transfers
- **WorkArea Indent** excluded from kitchen transfers
- Same inventory movement counted only once

### **Transfer Categorization:**
- **External Movements** → Store Transfers (vendors, other branches)
- **Internal Movements** → Kitchen Transfers (store↔kitchen, kitchen↔kitchen)

### **Consumption Calculation:**
- Uses **calculated consumption** from reconciliation formula
- Does **NOT** use consumption values from source data
- Ensures mathematical accuracy and consistency

---

## 📋 **Validation & Quality Checks**

### **Data Validation:**
```python
# Skip invalid entries
if not category or category == 'nan' or category == 'None':
    continue
if not subcategory or subcategory == 'nan' or subcategory == 'None':
    continue
```

### **Reconciliation Validation:**
- Opening + Transfers - Closing = Consumption
- Store + Kitchen = Total
- Category totals = Sum of subcategories
- Grand totals = Sum of all categories

### **Business Logic Validation:**
- Negative consumption indicates potential data issues
- Large variances require investigation
- Transfer amounts should balance across locations

---

## 🎯 **Key Benefits**

1. **Accurate Consumption Tracking** - Mathematical reconciliation ensures precision
2. **Location-wise Analysis** - Separate store and kitchen inventory tracking
3. **Transfer Transparency** - Detailed breakdown of all inventory movements
4. **Double-counting Prevention** - Careful handling of internal transfers
5. **Comprehensive Coverage** - All inventory movements captured and categorized

---

---

## 📊 **Breakdown Tables**

### **Kitchen Transfer Breakdown Table**
Shows detailed kitchen-level transfer analysis:

| Column | Formula | Description |
|--------|---------|-------------|
| WorkArea Indent (₹) | Sum of indent amounts | Store → Kitchen transfers (EXCLUDED) |
| WorkArea Transfer In (₹) | Sum of transfer_in | Kitchen → Kitchen inward |
| Total Inward (₹) | Transfer In Only | All inward movements (Indent Excluded) |
| WorkArea Transfer Out (₹) | Sum of transfer_out | Kitchen → Kitchen outward |
| Return To Store Out (₹) | Sum of return_to_store | Kitchen → Store returns |
| Total Outward (₹) | Transfer Out + Return To Store | All outward movements |
| Spoilage/Adjustments (₹) | Sum of spoilage_adjustments | Kitchen spoilage (±) |
| Net Transfer (₹) | Inward - Outward + Spoilage | Final kitchen net transfer |

### **Store Transfer Breakdown Table**
Shows detailed store-level transfer analysis:

| Column | Formula | Description |
|--------|---------|-------------|
| Purchase (₹) | Sum of purchase amounts | Vendor purchases |
| IBT In (₹) | Sum of ibt_in amounts | Inter-branch transfers in |
| Total Inward (₹) | Purchase + IBT In | All inward movements |
| IBT Out (₹) | Sum of ibt_out amounts | Inter-branch transfers out |
| Return Qty (₹) | Sum of return_qty amounts | Returns to vendors |
| Total Outward (₹) | IBT Out + Return Qty | All outward movements |
| Spoilage (₹) | Sum of spoilage amounts | Store spoilage adjustments |
| Net Transfer (₹) | Inward - Outward + Spoilage | Final store net transfer |

---

## 🔄 **Advanced Reconciliation Scenarios**

### **Multi-Location Handling:**
```python
# Location-based stock assignment
if 'kitchen' in location.lower():
    kitchen_stock += amount
elif 'store' in location.lower() or 'main' in location.lower():
    store_stock += amount
elif 'bar' in location.lower():
    store_stock += amount  # Bar treated as store extension
else:
    store_stock += amount  # Default to store
```

### **Transfer Direction Logic:**
```python
# Store Transfers (External)
store_inward = purchase + ibt_in
store_outward = ibt_out + abs(returns)
store_net = store_inward - store_outward + spoilage

# Kitchen Transfers (Internal)
kitchen_inward = transfer_in  # indent excluded
kitchen_outward = transfer_out + return_to_store
kitchen_net = kitchen_inward - kitchen_outward + spoilage_adjustments
```

### **Consumption Validation:**
```python
# Primary validation
calculated_consumption = (
    opening_total +
    store_transfers +
    kitchen_transfers -
    closing_total
)

# Cross-validation
if calculated_consumption < 0:
    # Investigate potential data issues
    # Check for missing transfers or incorrect stock values
```

---

## 📈 **Performance Optimization**

### **Parallel Processing:**
The reconciliation dashboard uses parallel execution for optimal performance:

```python
# Three parallel data sources
1. store_variance_df processing
2. inventory_consumption_df processing
3. sales_data processing

# Combined using optimized reconciliation logic
```

### **Data Structure Optimization:**
```python
# Efficient nested dictionary structure
reconciliation_table = {
    'category': {
        'subcategories': {
            'subcategory': {
                'opening_stock_store': 0,
                'opening_stock_kitchen': 0,
                # ... other fields
            }
        },
        'totals': { /* aggregated values */ }
    }
}
```

---

## 🎨 **UI Styling & Presentation**

### **Table Styling Configuration:**
- **Category Rows**: Orange background (`#ffe0b2`) with dark orange text (`#f57c00`)
- **Subcategory Rows**: White background with gray text
- **Grand Total Row**: Orange header background (`#ff9800`) with white text
- **Units**: Displayed only in column headers, not in data cells

### **Visual Hierarchy:**
1. **Category Level** - Bold orange styling for easy identification
2. **Subcategory Level** - Indented with lighter styling
3. **Grand Total** - Prominent styling for overall summary

---

## 🔍 **Troubleshooting Guide**

### **Common Issues:**

1. **Negative Consumption**
   - **Cause**: Closing stock > Opening stock + Transfers
   - **Solution**: Verify stock counts and transfer records

2. **Missing Categories**
   - **Cause**: Invalid category names or null values
   - **Solution**: Check data validation logic

3. **Transfer Imbalances**
   - **Cause**: Indent Out double-counting
   - **Solution**: Ensure Indent Out excluded from store transfers

4. **Performance Issues**
   - **Cause**: Large datasets or inefficient processing
   - **Solution**: Use parallel processing and optimized data structures

### **Data Quality Checks:**
```python
# Validation rules
- Category names must not be null/empty
- Subcategory names must not be null/empty
- Amounts must be numeric
- Location names must be valid
- Transfer amounts should balance
```

---

*This comprehensive documentation covers all aspects of the reconciliation table system. For specific implementation details, refer to the `create_reconciliation_table_data()` function and related methods in `dashboard_agents.py`.*
